/* ============
   Components
   ============ */

/* Header & Navigation */
/* Main navigation header only - not section headers */
body > header,
header:not(.quiz-header):not(.learn-header):not(.leaderboard-header):not(.admin-header) {
    background: var(--clr-header-bg);
    color: var(--clr-header-text);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    transition: background var(--transition), color var(--transition);
    /* Ensure header is always visible */
    opacity: 1;
    visibility: visible;
}

nav {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--clr-header-text);
}

.logo img {
    width: 2.5rem;
    height: 2.5rem;
    margin-right: 0.5rem;
    border-radius: 50%;
    background: var(--clr-secondary);
    transition: background var(--transition);
    padding: 4px;
    object-fit: cover;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 1.5rem;
}

.nav-links a {
    color: var(--clr-header-text);
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    transition: background var(--transition), transform var(--transition), color var(--transition);
    font-weight: 600;
    display: block;
    text-decoration: none;
}

.nav-links a:hover,
.nav-links a.active {
    background: var(--clr-secondary);
    transform: translateY(-2px);
    color: var(--clr-header-text);
}

/* Burger Menu */
.burger {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 25px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 1100;
    padding: 0;
    position: relative;
}

.burger .bar {
    width: 100%;
    height: 3px;
    background: var(--clr-header-text);
    border-radius: 2px;
    transition: all 0.3s ease-in-out;
    transform-origin: center;
}

.burger.change .bar:nth-child(1) { transform: translateY(8px) rotate(45deg); }
.burger.change .bar:nth-child(2) { opacity: 0; transform: scaleX(0); }
.burger.change .bar:nth-child(3) { transform: translateY(-8px) rotate(-45deg); }

/* Hero Section */
.hero {
    padding: 4rem var(--container-padding);
    text-align: center;
    color: #fff;
    border-radius: 0 0 var(--radius) var(--radius);
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                url('https://placehold.co/1200x400/2c3e50/ecf0f1?text=TerraTüftler+Hero') center/cover no-repeat;
    animation: fadeIn 1s ease-out both;
    margin-bottom: var(--section-margin-bottom);
    overflow: hidden;
}

.hero h1 { color: #fff; }
.hero p { font-size: 1.1rem; max-width: 700px; margin: 1rem auto 2rem; color: #fff; }

/* Features Section */
.features {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    animation: fadeIn 1s 0.3s ease-out both;
}

.feature-card {
    background: var(--clr-card);
    padding: 2rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition), box-shadow var(--transition);
    border: 1px solid var(--clr-border);
    display: flex;
    flex-direction: column;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-card h3 { color: var(--clr-secondary); margin-bottom: 0.5rem; }
.feature-card p { color: var(--clr-text-light); flex-grow: 1; margin-bottom: 1.5rem; }
.feature-card .btn { margin-top: auto; }

/* Quiz Components */
.quiz-section {
    margin-bottom: var(--section-margin-bottom);
}

.quiz-container {
    background: var(--clr-card);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    animation: fadeIn 1s ease-out both;
    margin-bottom: var(--section-margin-bottom);
    border: 1px solid var(--clr-border);
}

.quiz-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 1rem;
}

.quiz-header h2 { color: var(--clr-primary); margin-bottom: 0.5rem; }
.quiz-header p { color: var(--clr-text-light); margin-top: 0.5rem; }
.quiz-header .btn-back { margin-top: 1rem; }

/* Quiz Options */
.quiz-options {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.quiz-option {
    background: var(--clr-secondary);
    color: var(--clr-text-on-dark);
    padding: 1.5rem;
    border-radius: var(--radius);
    text-align: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition), box-shadow var(--transition), filter var(--transition), border-color var(--transition);
    border: 2px solid transparent;
}

.quiz-option:hover {
    filter: brightness(0.9);
    transform: scale(1.03);
    box-shadow: var(--shadow-md);
    border-color: var(--clr-primary);
}

.quiz-option h3 { color: inherit; margin-bottom: 0.5rem; }
.quiz-option p { color: inherit; opacity: 0.9; font-size: 0.9rem; }

/* Quiz Game Area */
.question-container h3 {
    color: var(--clr-primary);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.question-image {
    max-height: 300px;
    width: auto;
    max-width: 100%;
    border-radius: var(--radius);
    margin: 0 auto 1.5rem auto;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--clr-border);
    object-fit: contain;
    transition: all 0.3s ease;
}

/* Street View image states */
.question-image.streetview-available {
    position: relative;
}

/* Locked Street View state (before answering) */
.question-image.streetview-locked {
    border: 2px solid #bbb;
    box-shadow: var(--shadow-sm);
    position: relative;
    cursor: not-allowed;
    filter: grayscale(20%);
    transition: all 0.3s ease;
}

.question-image.streetview-locked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius);
    z-index: 5;
    pointer-events: none;
    transition: background 0.3s ease;
}

.question-image.streetview-locked:hover::before {
    background: rgba(0, 0, 0, 0.5);
}

.question-image.streetview-locked::after {
    content: "🔒 Beantworte die Frage, um Street View zu entsperren";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.question-image.streetview-locked:hover::after {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
}

/* Unlocked Street View state (after answering) */
.question-image.streetview-unlocked {
    border: 2px solid var(--clr-secondary);
    box-shadow: var(--shadow-md);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    filter: none;
}

.question-image.streetview-unlocked:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
    border-color: var(--clr-primary);
}

.question-image.streetview-unlocked:active {
    transform: scale(0.98);
}

.question-image.streetview-unlocked::after {
    content: "🌍 Street View verfügbar";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(52, 152, 219, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.question-image.streetview-unlocked:hover::after {
    opacity: 1;
    transform: translateY(-2px);
}

/* Unlock animation */
.question-image.streetview-unlocking {
    animation: unlockPulse 0.6s ease-out;
}

@keyframes unlockPulse {
    0% {
        transform: scale(1);
        border-color: #ccc;
    }
    50% {
        transform: scale(1.05);
        border-color: var(--clr-secondary);
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
    }
    100% {
        transform: scale(1);
        border-color: var(--clr-secondary);
    }
}



/* Answer Options */
.answer-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.answer-option {
    background: var(--clr-bg);
    padding: 1rem;
    border-radius: var(--radius);
    border: 2px solid var(--clr-border);
    cursor: pointer;
    text-align: center;
    font-weight: 600;
    transition: background var(--transition), transform var(--transition), border-color var(--transition), color var(--transition);
    color: var(--clr-text);
}

.answer-option:hover {
    background: var(--clr-secondary);
    color: var(--clr-text-on-dark);
    border-color: var(--clr-secondary);
    transform: translateY(-2px);
}

.answer-option.selected {
    background-color: var(--clr-secondary);
    color: var(--clr-text-on-dark);
    border-color: var(--clr-primary);
    box-shadow: var(--shadow-md);
}

.answer-option.correct {
    background-color: var(--clr-feedback-correct);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-correct);
    animation: pulse 0.5s ease-in-out;
}

.answer-option.incorrect {
    background-color: var(--clr-feedback-incorrect);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-incorrect);
    animation: shake 0.5s ease-in-out;
}

/* Feedback Message */
#feedback {
    padding: 1rem;
    border-radius: var(--radius);
    color: var(--clr-feedback-text);
    margin: 1rem 0;
    display: none;
    animation: fadeIn 0.5s ease-out both;
    text-align: center;
    font-weight: 600;
}

/* Timer Display */
.timer {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: none;
    animation: fadeIn 0.5s ease-out both;
    text-align: center;
    color: var(--clr-primary);
}

.timer span {
    color: var(--clr-secondary);
    min-width: 2ch;
    display: inline-block;
}

/* Quiz Controls */
.quiz-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.quiz-controls button {
    min-width: 120px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--clr-accent);
    color: var(--clr-text-on-dark);
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: background var(--transition), transform var(--transition), box-shadow var(--transition), filter var(--transition);
    text-align: center;
    font-family: inherit;
    font-size: 1rem;
}

.btn:hover {
    filter: brightness(0.9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary { background: var(--clr-secondary); }
.btn-back { background: var(--clr-button-back); }

.btn:disabled {
    background: var(--clr-button-disabled-bg);
    color: var(--clr-button-disabled-text);
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    filter: none;
}

/* Footer */
footer {
    background: var(--clr-footer-bg);
    color: var(--clr-footer-text);
    text-align: center;
    padding: 1.5rem var(--container-padding);
    margin-top: auto;
    border-top: 3px solid var(--clr-secondary);
    transition: background var(--transition), border-color var(--transition);
}

footer p {
    color: inherit;
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        flex-direction: column;
        position: fixed;
        top: 0;
        right: 0;
        width: min(80%, 300px);
        height: 100vh;
        background-color: var(--clr-header-bg);
        transform: translateX(100%);
        transition: transform 0.4s ease-in-out;
        z-index: 1050;
        padding-top: 5rem;
        box-shadow: -5px 0 15px rgba(0,0,0,0.2);
        overflow-y: auto;
        gap: 0;
    }

    .nav-links.show {
        transform: translateX(0);
    }

    .nav-links li {
        width: 100%;
    }

    .nav-links a {
        display: block;
        padding: 1rem 2rem;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 0;
        color: var(--clr-header-text);
        transform: none !important;
    }

    .nav-links a:hover,
    .nav-links a.active {
        background-color: var(--clr-secondary);
        transform: none;
        color: var(--clr-header-text);
    }

    .nav-links li:last-child a {
        border-bottom: none;
    }

    .burger {
        display: flex;
    }

    h1 { font-size: 2rem; }
    .hero h1 { font-size: 2rem; }
    .hero p { font-size: 1rem; }
    .features, .quiz-options, .learn-grid, .answer-options {
        grid-template-columns: 1fr;
    }
    .quiz-controls {
        flex-direction: column;
        align-items: stretch;
    }
    .quiz-controls button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    .quiz-controls button:last-child {
        margin-bottom: 0;
    }
    .quiz-container, .learn-container, .settings-container {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    .hero h1 { font-size: 1.8rem; }
    .hero p { font-size: 0.9rem; }
    nav { padding: 0 0.5rem; }
    header { padding: 0.75rem 0; }
    .logo { font-size: 1.1rem; }
    .logo img { width: 2rem; height: 2rem; }
    .btn { padding: 0.6rem 1.2rem; font-size: 0.9rem; }
    .quiz-option, .answer-option { padding: 1rem; }
    .quiz-container, .learn-container, .settings-container { padding: 1rem; }
}

/* Settings Toggle Switch Styling */
.setting-item input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 50px;
    height: 26px;
    background-color: #ccc;
    border-radius: 13px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
    border: 1px solid var(--clr-border);
    flex-shrink: 0; /* Prevent shrinking in flex container */
}

body.theme-dark .setting-item input[type="checkbox"] {
    background-color: var(--clr-border); /* Use border color for off state in dark */
}

.setting-item input[type="checkbox"]::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 3px;
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.setting-item input[type="checkbox"]:checked {
    background-color: var(--clr-accent);
}

.setting-item input[type="checkbox"]:checked::before {
    transform: translateX(24px);
}

/* Content Management Styles */
.content-tabs {
    display: flex;
    border-bottom: 2px solid var(--clr-border);
    margin-bottom: 2rem;
    background: var(--clr-bg-secondary);
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: var(--clr-text-secondary);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    text-align: center;
}

.tab-btn:hover {
    color: var(--clr-primary);
    background-color: rgba(var(--clr-primary-rgb), 0.1);
}

.tab-btn.active {
    color: var(--clr-primary);
    background-color: var(--clr-bg-primary);
    border-bottom-color: var(--clr-primary);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Content header - base styles */
.content-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--clr-primary);
}

.content-header h4 {
    margin-bottom: 0.75rem;
    color: var(--clr-text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.content-header p {
    color: var(--clr-text-secondary);
    margin-bottom: 0;
    line-height: 1.5;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--clr-bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--clr-border);
}

.filter-controls select {
    flex: 1;
    max-width: 200px;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--clr-border);
    border-radius: 6px;
    background: var(--clr-bg-primary);
    color: var(--clr-text-primary);
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--clr-primary);
    box-shadow: 0 0 0 2px rgba(var(--clr-primary-rgb), 0.2);
}

.content-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--clr-border);
    border-radius: 12px;
    background: var(--clr-bg-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--clr-border);
    transition: all 0.3s ease;
    position: relative;
}

.content-item:last-child {
    border-bottom: none;
}

.content-item:hover {
    background-color: var(--clr-bg-hover);
    transform: translateX(4px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.content-item-info {
    flex: 1;
    min-width: 0; /* Allow text truncation */
}

.content-item-title {
    font-weight: 700; /* Increased font weight for better visibility */
    color: var(--clr-text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.15rem; /* Slightly increased font size */
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 1.4; /* Added line height for better readability */
}

.content-item-meta {
    font-size: 0.95rem; /* Increased from 0.875rem for better readability */
    color: var(--clr-text-primary); /* Changed from secondary to primary for better contrast */
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.content-item-meta span {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem; /* Increased padding for better touch targets */
    background: var(--clr-bg-secondary);
    border-radius: 6px; /* Slightly larger border radius */
    font-size: 0.875rem; /* Increased from 0.8rem for better readability */
    font-weight: 600; /* Increased font weight for better visibility */
    border: 1px solid var(--clr-border); /* Added border for better definition */
}

.content-item-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: 1px solid #dc3545;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
    border: 1px solid #17a2b8;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
    border: 1px solid #ffc107;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    border: 1px solid #6c757d;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #545b62 100%);
    border-color: #545b62;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

/* Delete Confirmation Modal Styles */
.delete-warning {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.warning-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.warning-content {
    flex: 1;
}

.delete-details {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #dc3545;
}

.delete-details h5 {
    margin: 0 0 0.5rem 0;
    color: var(--clr-text-primary);
    font-size: 0.9rem;
}

.delete-details ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--clr-text-secondary);
    font-size: 0.875rem;
}

.delete-details li {
    margin-bottom: 0.25rem;
}

.loading-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--clr-text-secondary);
    background: var(--clr-bg-secondary);
    border-radius: 8px;
    margin: 1rem 0;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

/* Question preview images */
.question-preview-mini {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    border: 2px solid var(--clr-border);
    margin-right: 0.5rem;
}

.question-text-preview {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500; /* Added font weight for better visibility */
    color: var(--clr-text-primary); /* Ensure primary text color */
}

/* Question preview in content management */
.question-preview-mini {
    max-width: 60px;
    max-height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--clr-border);
}

.question-text-preview {
    max-width: 350px; /* Increased max width for more text visibility */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500; /* Added font weight for better visibility */
    color: var(--clr-text-primary); /* Ensure primary text color */
    font-size: 0.95rem; /* Added explicit font size */
}

/* Add other specific component styles here if needed */