<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TerraTüftler Deployment Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>TerraTüftler Deployment Test</h1>
    <p>This page tests if all data files are loading correctly on your deployment.</p>
    
    <div id="test-results"></div>
    
    <script>
        const resultsDiv = document.getElementById('test-results');
        
        function addResult(test, status, message, data = null) {
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            div.innerHTML = `
                <strong>${test}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>` : ''}
            `;
            resultsDiv.appendChild(div);
        }
        
        async function testDataFile(filename, description) {
            addResult(description, 'loading', 'Testing...');
            
            try {
                const response = await fetch(`/data/${filename}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Invalid content-type: ${contentType}`);
                }
                
                const data = await response.json();
                
                // Remove loading message
                resultsDiv.removeChild(resultsDiv.lastChild);
                
                addResult(description, 'success', 
                    `✅ Loaded successfully (${Object.keys(data).length} top-level keys)`, 
                    Object.keys(data)
                );
                
                return data;
            } catch (error) {
                // Remove loading message
                resultsDiv.removeChild(resultsDiv.lastChild);
                
                addResult(description, 'error', `❌ Failed: ${error.message}`);
                return null;
            }
        }
        
        async function testFontLoading() {
            addResult('Google Fonts', 'loading', 'Testing font loading...');
            
            try {
                const response = await fetch('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;600;700&display=swap');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('text/css')) {
                    throw new Error(`Invalid content-type: ${contentType}`);
                }
                
                // Remove loading message
                resultsDiv.removeChild(resultsDiv.lastChild);
                
                addResult('Google Fonts', 'success', '✅ Font CSS loads correctly');
            } catch (error) {
                // Remove loading message
                resultsDiv.removeChild(resultsDiv.lastChild);
                
                addResult('Google Fonts', 'error', `❌ Font loading failed: ${error.message}`);
            }
        }
        
        async function runTests() {
            addResult('Deployment Test', 'loading', 'Starting tests...');
            
            // Test data files
            const quizData = await testDataFile('quizData.json', 'Quiz Data');
            const learningData = await testDataFile('learningData.json', 'Learning Data');
            const leaderboardData = await testDataFile('leaderboardData.json', 'Leaderboard Data');
            
            // Test font loading
            await testFontLoading();
            
            // Remove initial loading message
            resultsDiv.removeChild(resultsDiv.firstChild);
            
            // Summary
            const allPassed = quizData && learningData && leaderboardData;
            addResult('Overall Status', allPassed ? 'success' : 'error', 
                allPassed ? '✅ All tests passed! Deployment should work correctly.' : 
                           '❌ Some tests failed. Check the errors above and redeploy.');
        }
        
        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
