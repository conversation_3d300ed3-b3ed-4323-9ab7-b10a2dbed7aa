{"name": "terratuft<PERSON>", "version": "1.0.0", "description": "A geography quiz game similar to GeoGuessr", "type": "module", "scripts": {"dev": "vite --host", "start": "node server.js", "build": "vite build", "vercel-build": "vite build", "preview": "vite preview", "clean": "rimraf dist node_modules/.vite", "dev:frontend": "vite --host", "dev:backend": "node server.js", "dev:full": "npm run build && npm run start"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "leaflet": "^1.9.4", "multer": "^2.0.1"}, "devDependencies": {"vite": "^5.0.0"}}