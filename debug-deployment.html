<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TerraTüftler Deployment Debug Tool</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border: 1px solid #ffeaa7; 
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            max-height: 300px;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .url-test a {
            color: #007bff;
            text-decoration: none;
        }
        .url-test a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TerraTüftler Deployment Debug Tool</h1>
        <p>This tool helps diagnose data loading issues on the deployed Vercel application.</p>
        
        <div class="test-section">
            <h2>📍 Environment Detection</h2>
            <div id="environment-info"></div>
        </div>

        <div class="test-section">
            <h2>🌐 Direct URL Tests</h2>
            <p>Test if data files are accessible via direct URLs:</p>
            <div id="url-tests"></div>
        </div>

        <div class="test-section">
            <h2>📊 Data Loading Simulation</h2>
            <button onclick="simulateDataLoading()">🚀 Simulate TerraTüftler Data Loading</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Network Analysis</h2>
            <button onclick="analyzeNetworkRequests()">📡 Analyze Network Requests</button>
            <div id="network-analysis"></div>
        </div>

        <div class="test-section">
            <h2>🧪 Quiz Data Validation</h2>
            <button onclick="validateQuizData()">✅ Validate Quiz Data Structure</button>
            <div id="validation-results"></div>
        </div>

        <div class="test-section">
            <h2>📝 Console Logs</h2>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
            <div id="console-logs"></div>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];

        function captureLog(type, args) {
            const timestamp = new Date().toISOString();
            logs.push({ type, timestamp, message: Array.from(args).join(' ') });
            updateConsoleLogs();
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            captureLog('log', args);
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            captureLog('error', args);
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            captureLog('warn', args);
        };

        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.innerHTML = logs.slice(-20).map(log => 
                `<div class="test-result ${log.type === 'error' ? 'error' : log.type === 'warn' ? 'warning' : 'info'}">
                    <strong>[${log.timestamp}] ${log.type.toUpperCase()}:</strong> ${log.message}
                </div>`
            ).join('');
        }

        function clearLogs() {
            logs.length = 0;
            updateConsoleLogs();
        }

        function addResult(containerId, type, message, details = null) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `
                <strong>${message}</strong>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            container.appendChild(div);
        }

        // Environment Detection
        function detectEnvironment() {
            const info = {
                hostname: window.location.hostname,
                protocol: window.location.protocol,
                port: window.location.port,
                pathname: window.location.pathname,
                userAgent: navigator.userAgent,
                isVercel: window.location.hostname.includes('vercel.app'),
                isLocalhost: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            };

            const envDiv = document.getElementById('environment-info');
            envDiv.innerHTML = `
                <div class="test-result info">
                    <strong>Environment Information:</strong>
                    <pre>${JSON.stringify(info, null, 2)}</pre>
                </div>
            `;

            return info;
        }

        // URL Tests
        function createUrlTests() {
            const baseUrl = window.location.origin;
            const dataFiles = [
                'quizData.json',
                'learningData.json', 
                'leaderboardData.json'
            ];

            const urlTestsDiv = document.getElementById('url-tests');
            urlTestsDiv.innerHTML = dataFiles.map(file => `
                <div class="url-test">
                    <strong>${file}:</strong>
                    <a href="${baseUrl}/data/${file}" target="_blank">${baseUrl}/data/${file}</a>
                    <button onclick="testUrl('${baseUrl}/data/${file}', '${file}')">Test</button>
                    <div id="url-result-${file}"></div>
                </div>
            `).join('');
        }

        async function testUrl(url, filename) {
            const resultDiv = document.getElementById(`url-result-${filename}`);
            resultDiv.innerHTML = '<div class="test-result warning">Testing...</div>';

            try {
                const response = await fetch(url);
                const contentType = response.headers.get('content-type');
                const status = response.status;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${status}: ${response.statusText}`);
                }

                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (parseError) {
                    throw new Error(`JSON Parse Error: ${parseError.message}`);
                }

                resultDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ Success - Status: ${status}, Content-Type: ${contentType}
                        <br>Data keys: ${Object.keys(data).join(', ')}
                        <br>Size: ${text.length} characters
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Failed: ${error.message}
                    </div>
                `;
            }
        }

        // Simulate TerraTüftler data loading
        async function simulateDataLoading() {
            const resultsDiv = document.getElementById('data-loading-results');
            resultsDiv.innerHTML = '<div class="test-result warning">Simulating data loading...</div>';

            try {
                // Simulate the exact loading process from TerraTüftler
                console.log('🔄 Starting data loading simulation...');
                
                // Check backend availability (should be false on Vercel)
                const isVercel = window.location.hostname.includes('vercel.app');
                console.log('Detected Vercel deployment:', isVercel);

                // Load quiz data
                console.log('📊 Loading quiz data...');
                const quizResponse = await fetch('/data/quizData.json');
                console.log('Quiz response status:', quizResponse.status);
                console.log('Quiz response headers:', Object.fromEntries(quizResponse.headers.entries()));

                if (!quizResponse.ok) {
                    throw new Error(`Quiz data HTTP error! status: ${quizResponse.status}`);
                }

                const quizContentType = quizResponse.headers.get('content-type');
                if (!quizContentType || !quizContentType.includes('application/json')) {
                    console.warn('Quiz response is not JSON, content-type:', quizContentType);
                    const text = await quizResponse.text();
                    console.error('Quiz response text:', text.substring(0, 200));
                    throw new Error('Invalid JSON response for quiz data');
                }

                const quizData = await quizResponse.json();
                console.log('✅ Quiz data loaded successfully');
                console.log('Quiz data structure:', Object.keys(quizData));

                // Load learning data
                console.log('📚 Loading learning data...');
                const learningResponse = await fetch('/data/learningData.json');
                const learningData = await learningResponse.json();
                console.log('✅ Learning data loaded successfully');

                // Load leaderboard data
                console.log('🏆 Loading leaderboard data...');
                const leaderboardResponse = await fetch('/data/leaderboardData.json');
                const leaderboardData = await leaderboardResponse.json();
                console.log('✅ Leaderboard data loaded successfully');

                // Validate data structure
                const hasValidQuizData = quizData && 
                    (Object.keys(quizData).length > 0) &&
                    (quizData['time-limited'] || quizData['image-based'] || quizData.questions);

                resultsDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ Data loading simulation completed successfully!
                        <br><strong>Quiz Data:</strong> ${Object.keys(quizData).length} top-level keys
                        <br><strong>Learning Data:</strong> ${Object.keys(learningData).length} top-level keys  
                        <br><strong>Leaderboard Data:</strong> ${Object.keys(leaderboardData).length} top-level keys
                        <br><strong>Valid Quiz Structure:</strong> ${hasValidQuizData ? 'Yes' : 'No'}
                        <pre>Quiz Data Keys: ${JSON.stringify(Object.keys(quizData), null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                console.error('❌ Data loading simulation failed:', error);
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Data loading simulation failed: ${error.message}
                        <br>Check console logs for detailed error information.
                    </div>
                `;
            }
        }

        async function analyzeNetworkRequests() {
            const resultsDiv = document.getElementById('network-analysis');
            resultsDiv.innerHTML = '<div class="test-result info">Analyzing network requests...</div>';

            // This would require Performance API or manual tracking
            resultsDiv.innerHTML = `
                <div class="test-result info">
                    <strong>Network Analysis Instructions:</strong>
                    <br>1. Open browser Developer Tools (F12)
                    <br>2. Go to Network tab
                    <br>3. Refresh the page or run data loading simulation
                    <br>4. Look for requests to /data/*.json files
                    <br>5. Check status codes, response headers, and response content
                    <br>6. Look for any 404, 500, or CORS errors
                </div>
            `;
        }

        async function validateQuizData() {
            const resultsDiv = document.getElementById('validation-results');
            resultsDiv.innerHTML = '<div class="test-result warning">Validating quiz data...</div>';

            try {
                const response = await fetch('/data/quizData.json');
                const data = await response.json();

                const validationResults = [];

                // Check basic structure
                if (typeof data === 'object' && data !== null) {
                    validationResults.push('✅ Data is a valid object');
                } else {
                    validationResults.push('❌ Data is not a valid object');
                }

                // Check for expected keys
                const expectedKeys = ['time-limited', 'image-based', 'all'];
                const hasExpectedKeys = expectedKeys.some(key => data.hasOwnProperty(key));
                if (hasExpectedKeys) {
                    validationResults.push('✅ Has expected quiz mode keys');
                } else {
                    validationResults.push('❌ Missing expected quiz mode keys');
                }

                // Check categories
                let totalQuestions = 0;
                Object.keys(data).forEach(mode => {
                    if (typeof data[mode] === 'object') {
                        Object.keys(data[mode]).forEach(category => {
                            if (Array.isArray(data[mode][category])) {
                                totalQuestions += data[mode][category].length;
                            }
                        });
                    }
                });

                validationResults.push(`📊 Total questions found: ${totalQuestions}`);

                resultsDiv.innerHTML = `
                    <div class="test-result ${totalQuestions > 0 ? 'success' : 'error'}">
                        <strong>Quiz Data Validation Results:</strong>
                        <br>${validationResults.join('<br>')}
                        <pre>Data Structure: ${JSON.stringify(Object.keys(data), null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Validation failed: ${error.message}
                    </div>
                `;
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            detectEnvironment();
            createUrlTests();
            console.log('🔍 TerraTüftler Debug Tool initialized');
        });
    </script>
</body>
</html>
