{"buildCommand": "npm run build", "outputDirectory": "dist", "headers": [{"source": "/data/(.*)", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}