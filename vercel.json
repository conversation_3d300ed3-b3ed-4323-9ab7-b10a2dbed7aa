{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/data/quizData.json", "dest": "/data/quizData.json", "headers": {"Content-Type": "application/json", "Cache-Control": "public, max-age=3600"}}, {"src": "/data/learningData.json", "dest": "/data/learningData.json", "headers": {"Content-Type": "application/json", "Cache-Control": "public, max-age=3600"}}, {"src": "/data/leaderboardData.json", "dest": "/data/leaderboardData.json", "headers": {"Content-Type": "application/json", "Cache-Control": "public, max-age=3600"}}, {"src": "/data/(.*)", "dest": "/data/$1", "headers": {"Content-Type": "application/json", "Cache-Control": "public, max-age=3600"}}, {"src": "/assets/(.*)", "dest": "/assets/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/data/(.*\\.json)", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/(.*\\.css)", "headers": [{"key": "Content-Type", "value": "text/css"}]}, {"source": "/(.*\\.js)", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}], "functions": {}, "rewrites": [{"source": "/api/(.*)", "destination": "/404"}]}