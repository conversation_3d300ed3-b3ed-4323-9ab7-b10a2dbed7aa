{"buildCommand": "npm run build", "outputDirectory": "dist", "headers": [{"source": "/data/(.*\\.json)", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/(.*\\.css)", "headers": [{"key": "Content-Type", "value": "text/css"}]}, {"source": "/(.*\\.js)", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}